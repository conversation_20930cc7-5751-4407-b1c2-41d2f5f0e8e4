# Authentication Modes and Health Check Features

This document describes the new authentication modes and health check features implemented in RustyCluster.

## Authentication Modes

### Overview

RustyCluster now supports two authentication modes to optimize performance based on your security requirements:

1. **per_request** (default): Validates session token on each request
2. **connection_only**: Authenticates once per connection, no token validation per request

### Configuration

```toml
# Authentication mode: "connection_only" or "per_request"
auth_mode = "connection_only"

# Enable/disable session token expiry
auth_token_expiry_enabled = false
```

### Authentication Mode Details

#### per_request Mode (Default)
- **Security**: High - validates session token on every request
- **Performance**: Standard - adds validation overhead per request
- **Use Case**: Production environments requiring maximum security
- **Token Expiry**: Recommended to keep enabled

#### connection_only Mode
- **Security**: Medium - validates only during connection establishment
- **Performance**: High - no per-request validation overhead
- **Use Case**: High-performance environments with trusted networks
- **Token Expiry**: Can be disabled for permanent tokens

### Token Expiry Configuration

```toml
# Enable/disable session token expiry
# true: tokens expire after session_duration_secs (default)
# false: tokens are permanent until application restart
auth_token_expiry_enabled = true

# Session duration in seconds (only applies when expiry is enabled)
session_duration_secs = 3600
```

### Performance Impact

| Mode | RPS Impact | Security Level | Recommended For |
|------|------------|----------------|-----------------|
| per_request | Baseline | High | Production, untrusted networks |
| connection_only | +10-15% | Medium | High-performance, trusted networks |

## Health Check and Keep-Alive Features

### Overview

RustyCluster now includes comprehensive health check mechanisms to maintain connection health during idle periods. This prevents connection timeouts and ensures optimal performance.

### Supported Connection Types

1. **Redis Connections**: Primary Redis instance
2. **Secondary Nodes**: Rust node connections for replication
3. **Site Replication Nodes**: Cross-site replication connections
4. **Peer Redis Nodes**: Direct Redis connections for write consistency

### Configuration

```toml
# Redis connection health checks
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 30

# Secondary nodes health checks
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 30

# Site replication nodes health checks
site_nodes_keepalive_enabled = true
site_nodes_keepalive_interval_secs = 30

# Peer Redis nodes health checks
peer_redis_keepalive_enabled = true
peer_redis_keepalive_interval_secs = 30
```

### Health Check Mechanisms

#### Redis Connections
- **Method**: PING command
- **Purpose**: Keep Redis connections alive
- **Frequency**: Configurable interval (default: 30 seconds)

#### Secondary Nodes
- **Method**: gRPC ping request
- **Purpose**: Maintain Rust node connections
- **Frequency**: Configurable interval (default: 30 seconds)

#### Site Replication Nodes
- **Method**: Connection pool health checks
- **Purpose**: Keep cross-site connections active
- **Frequency**: Configurable interval (default: 30 seconds)

#### Peer Redis Nodes
- **Method**: Redis PING to peer nodes
- **Purpose**: Maintain direct Redis connections for write consistency
- **Frequency**: Configurable interval (default: 30 seconds)

### Benefits

1. **Connection Stability**: Prevents idle connection timeouts
2. **Performance**: Reduces connection re-establishment overhead
3. **Reliability**: Early detection of connection issues
4. **Monitoring**: Health status logging for troubleshooting

### Recommended Settings

#### High-Performance Environment
```toml
# Frequent health checks for maximum reliability
redis_keepalive_interval_secs = 15
secondary_nodes_keepalive_interval_secs = 15
site_nodes_keepalive_interval_secs = 15
peer_redis_keepalive_interval_secs = 15
```

#### Standard Environment
```toml
# Balanced health checks
redis_keepalive_interval_secs = 30
secondary_nodes_keepalive_interval_secs = 30
site_nodes_keepalive_interval_secs = 30
peer_redis_keepalive_interval_secs = 30
```

#### Low-Resource Environment
```toml
# Less frequent health checks to reduce overhead
redis_keepalive_interval_secs = 60
secondary_nodes_keepalive_interval_secs = 60
site_nodes_keepalive_interval_secs = 60
peer_redis_keepalive_interval_secs = 60
```

## Example Configurations

### High-Performance Setup
```toml
# Authentication optimized for performance
auth_mode = "connection_only"
auth_token_expiry_enabled = false

# Aggressive health checks
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 15
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 15
```

### Security-Focused Setup
```toml
# Authentication optimized for security
auth_mode = "per_request"
auth_token_expiry_enabled = true
session_duration_secs = 1800  # 30 minutes

# Standard health checks
redis_keepalive_enabled = true
redis_keepalive_interval_secs = 30
secondary_nodes_keepalive_enabled = true
secondary_nodes_keepalive_interval_secs = 30
```

## Migration Guide

### From Previous Versions

1. **Add new configuration parameters** to your config.toml
2. **Choose authentication mode** based on your security requirements
3. **Enable health checks** for connection types you use
4. **Adjust intervals** based on your environment

### Testing

1. **Load test** with your chosen authentication mode
2. **Monitor logs** for health check activity
3. **Verify connection stability** during idle periods
4. **Measure performance impact** of health checks

## Health Check Issue Fixes

### Issue 1: Authentication Required for Health Checks
**Problem**: Health checks to secondary nodes were failing with authentication errors when `auth_enabled=true`.

**Root Cause**: Health check ping requests were going through the regular `ping` method which requires authentication.

**Solution**:
- Added `ping_health_check` method that bypasses authentication
- Health checks now handle authentication failures gracefully
- When authentication fails, it's logged as a warning but considered a successful connection test

### Issue 2: Health Checks Running During Traffic Flow
**Problem**: Health checks were running continuously every 30 seconds regardless of traffic, potentially interfering with active operations.

**Current Behavior**: Health checks run on fixed intervals (configurable)
**Recommendation**: Consider implementing adaptive health checks that:
- Reduce frequency during high traffic periods
- Increase frequency during idle periods
- Use connection activity as a health indicator

### Issue 3: Incomplete Health Check Coverage
**Problem**: Only secondary nodes were being health checked. Redis URL, site primary, and failover nodes had no health checks.

**Solution**:
- **Redis Connections**: Now properly health checked with PING commands
- **Site Replication Nodes**: Implemented health checks for both primary and failover nodes
- **Peer Redis Nodes**: Health checks for write consistency Redis connections
- **Secondary Nodes**: Improved to handle authentication gracefully

### Health Check Authentication Handling

Health checks now handle authentication in the following way:

1. **Direct Redis Connections**: No authentication required (Redis PING)
2. **Secondary Nodes**: Attempts ping, treats authentication failures as connection success
3. **Site Replication Nodes**: Same as secondary nodes
4. **Peer Redis Nodes**: Direct Redis PING, no gRPC authentication

### Configuration Recommendations

For optimal health check behavior:

```toml
# Enable all health checks
redis_keepalive_enabled = true
secondary_nodes_keepalive_enabled = true
site_nodes_keepalive_enabled = true
peer_redis_keepalive_enabled = true

# Adjust intervals based on your environment
# Higher frequency for critical environments
redis_keepalive_interval_secs = 30
secondary_nodes_keepalive_interval_secs = 30
site_nodes_keepalive_interval_secs = 30
peer_redis_keepalive_interval_secs = 30
```

## Troubleshooting

### Authentication Issues
- Check `auth_mode` setting matches your client expectations
- Verify `auth_token_expiry_enabled` setting
- Monitor session creation/validation logs

### Health Check Issues
- **Authentication Failures**: Normal when auth is enabled - check for connection success logs
- **Network Connectivity**: Verify all configured nodes are reachable
- **Interval Tuning**: Adjust intervals based on network stability and load
- **Missing Health Checks**: Ensure all connection types are configured and enabled
