use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::time::interval;
use tracing::{debug, info, warn, error};
use deadpool_redis::Pool;
use crate::write_consistency::{PeerRedisPool, SiteReplicationManager};
use crate::grpc::rustycluster::PingRequest;
use crate::grpc::rustycluster::key_value_service_client::KeyValueServiceClient;
use parking_lot::RwLock;

/// Health check manager for maintaining connection health across all connection types
pub struct HealthCheckManager {
    // Redis connection health check
    redis_pool: Option<Arc<Pool>>,
    redis_keepalive_enabled: bool,
    redis_keepalive_interval: Duration,
    redis_idle_threshold: Duration,
    redis_last_activity: Arc<RwLock<Instant>>,

    // Secondary nodes health check
    secondary_nodes: Vec<String>,
    secondary_nodes_keepalive_enabled: bool,
    secondary_nodes_keepalive_interval: Duration,
    secondary_nodes_idle_threshold: Duration,
    secondary_nodes_last_activity: Arc<RwLock<Instant>>,

    // Site replication nodes health check
    site_replication_manager: Option<Arc<tokio::sync::Mutex<SiteReplicationManager>>>,
    site_nodes_keepalive_enabled: bool,
    site_nodes_keepalive_interval: Duration,
    site_nodes_idle_threshold: Duration,
    site_nodes_last_activity: Arc<RwLock<Instant>>,

    // Peer Redis nodes health check
    peer_redis_pool: Option<Arc<PeerRedisPool>>,
    peer_redis_keepalive_enabled: bool,
    peer_redis_keepalive_interval: Duration,
    peer_redis_idle_threshold: Duration,
    peer_redis_last_activity: Arc<RwLock<Instant>>,
}

impl HealthCheckManager {
    pub fn new(
        redis_pool: Option<Arc<Pool>>,
        redis_keepalive_enabled: bool,
        redis_keepalive_interval_secs: u64,
        redis_idle_threshold_secs: u64,
        secondary_nodes: Vec<String>,
        secondary_nodes_keepalive_enabled: bool,
        secondary_nodes_keepalive_interval_secs: u64,
        secondary_nodes_idle_threshold_secs: u64,
        site_replication_manager: Option<Arc<tokio::sync::Mutex<SiteReplicationManager>>>,
        site_nodes_keepalive_enabled: bool,
        site_nodes_keepalive_interval_secs: u64,
        site_nodes_idle_threshold_secs: u64,
        peer_redis_pool: Option<Arc<PeerRedisPool>>,
        peer_redis_keepalive_enabled: bool,
        peer_redis_keepalive_interval_secs: u64,
        peer_redis_idle_threshold_secs: u64,
    ) -> Self {
        let now = Instant::now();
        Self {
            redis_pool,
            redis_keepalive_enabled,
            redis_keepalive_interval: Duration::from_secs(redis_keepalive_interval_secs),
            redis_idle_threshold: Duration::from_secs(redis_idle_threshold_secs),
            redis_last_activity: Arc::new(RwLock::new(now)),
            secondary_nodes,
            secondary_nodes_keepalive_enabled,
            secondary_nodes_keepalive_interval: Duration::from_secs(secondary_nodes_keepalive_interval_secs),
            secondary_nodes_idle_threshold: Duration::from_secs(secondary_nodes_idle_threshold_secs),
            secondary_nodes_last_activity: Arc::new(RwLock::new(now)),
            site_replication_manager,
            site_nodes_keepalive_enabled,
            site_nodes_keepalive_interval: Duration::from_secs(site_nodes_keepalive_interval_secs),
            site_nodes_idle_threshold: Duration::from_secs(site_nodes_idle_threshold_secs),
            site_nodes_last_activity: Arc::new(RwLock::new(now)),
            peer_redis_pool,
            peer_redis_keepalive_enabled,
            peer_redis_keepalive_interval: Duration::from_secs(peer_redis_keepalive_interval_secs),
            peer_redis_idle_threshold: Duration::from_secs(peer_redis_idle_threshold_secs),
            peer_redis_last_activity: Arc::new(RwLock::new(now)),
        }
    }

    /// Start all health check tasks
    pub fn start_health_checks(self: Arc<Self>) {
        info!("Starting idle-based health check manager");

        // Start Redis health check
        if self.redis_keepalive_enabled && self.redis_pool.is_some() {
            info!("Starting Redis idle-based health check task with check interval: {:?}, idle threshold: {:?}",
                  self.redis_keepalive_interval, self.redis_idle_threshold);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.redis_health_check_task().await;
            });
        }

        // Start secondary nodes health check
        if self.secondary_nodes_keepalive_enabled && !self.secondary_nodes.is_empty() {
            info!("Starting secondary nodes idle-based health check task with check interval: {:?}, idle threshold: {:?}",
                  self.secondary_nodes_keepalive_interval, self.secondary_nodes_idle_threshold);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.secondary_nodes_health_check_task().await;
            });
        }

        // Start site replication nodes health check
        if self.site_nodes_keepalive_enabled && self.site_replication_manager.is_some() {
            info!("Starting site replication nodes idle-based health check task with check interval: {:?}, idle threshold: {:?}",
                  self.site_nodes_keepalive_interval, self.site_nodes_idle_threshold);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.site_nodes_health_check_task().await;
            });
        }

        // Start peer Redis nodes health check
        if self.peer_redis_keepalive_enabled && self.peer_redis_pool.is_some() {
            info!("Starting peer Redis nodes idle-based health check task with check interval: {:?}, idle threshold: {:?}",
                  self.peer_redis_keepalive_interval, self.peer_redis_idle_threshold);
            let manager_clone = self.clone();
            tokio::spawn(async move {
                manager_clone.peer_redis_health_check_task().await;
            });
        }
    }

    /// Update Redis activity timestamp
    pub fn update_redis_activity(&self) {
        *self.redis_last_activity.write() = Instant::now();
    }

    /// Update secondary nodes activity timestamp
    pub fn update_secondary_nodes_activity(&self) {
        *self.secondary_nodes_last_activity.write() = Instant::now();
    }

    /// Update site nodes activity timestamp
    pub fn update_site_nodes_activity(&self) {
        *self.site_nodes_last_activity.write() = Instant::now();
    }

    /// Update peer Redis activity timestamp
    pub fn update_peer_redis_activity(&self) {
        *self.peer_redis_last_activity.write() = Instant::now();
    }

    /// Redis connection health check task - only runs when connections are idle
    async fn redis_health_check_task(&self) {
        let mut interval_timer = interval(self.redis_keepalive_interval);

        loop {
            interval_timer.tick().await;

            // Check if connections have been idle for longer than the threshold
            let last_activity = *self.redis_last_activity.read();
            let idle_duration = last_activity.elapsed();

            if idle_duration >= self.redis_idle_threshold {
                debug!("Redis connections idle for {:?}, performing health check", idle_duration);

                if let Some(pool) = &self.redis_pool {
                    match self.ping_redis_connection(pool).await {
                        Ok(_) => {
                            debug!("Redis health check successful after {:?} idle time", idle_duration);
                        }
                        Err(e) => {
                            warn!("Redis health check failed after {:?} idle time: {}", idle_duration, e);
                        }
                    }
                }
            } else {
                debug!("Redis connections active (idle for {:?}), skipping health check", idle_duration);
            }
        }
    }

    /// Secondary nodes health check task - only runs when connections are idle
    async fn secondary_nodes_health_check_task(&self) {
        let mut interval_timer = interval(self.secondary_nodes_keepalive_interval);

        loop {
            interval_timer.tick().await;

            // Check if connections have been idle for longer than the threshold
            let last_activity = *self.secondary_nodes_last_activity.read();
            let idle_duration = last_activity.elapsed();

            if idle_duration >= self.secondary_nodes_idle_threshold {
                debug!("Secondary nodes connections idle for {:?}, performing health check", idle_duration);

                for node in &self.secondary_nodes {
                    match self.ping_secondary_node(node).await {
                        Ok(_) => {
                            debug!("Secondary node {} health check successful after {:?} idle time", node, idle_duration);
                        }
                        Err(e) => {
                            warn!("Secondary node {} health check failed after {:?} idle time: {}", node, idle_duration, e);
                        }
                    }
                }
            } else {
                debug!("Secondary nodes connections active (idle for {:?}), skipping health check", idle_duration);
            }
        }
    }

    /// Site replication nodes health check task - only runs when connections are idle
    async fn site_nodes_health_check_task(&self) {
        let mut interval_timer = interval(self.site_nodes_keepalive_interval);

        loop {
            interval_timer.tick().await;

            // Check if connections have been idle for longer than the threshold
            let last_activity = *self.site_nodes_last_activity.read();
            let idle_duration = last_activity.elapsed();

            if idle_duration >= self.site_nodes_idle_threshold {
                debug!("Site replication nodes connections idle for {:?}, performing health check", idle_duration);

                if let Some(site_manager) = &self.site_replication_manager {
                    match self.ping_site_replication_nodes(site_manager).await {
                        Ok(_) => {
                            debug!("Site replication nodes health check successful after {:?} idle time", idle_duration);
                        }
                        Err(e) => {
                            warn!("Site replication nodes health check failed after {:?} idle time: {}", idle_duration, e);
                        }
                    }
                }
            } else {
                debug!("Site replication nodes connections active (idle for {:?}), skipping health check", idle_duration);
            }
        }
    }

    /// Peer Redis nodes health check task - only runs when connections are idle
    async fn peer_redis_health_check_task(&self) {
        let mut interval_timer = interval(self.peer_redis_keepalive_interval);

        loop {
            interval_timer.tick().await;

            // Check if connections have been idle for longer than the threshold
            let last_activity = *self.peer_redis_last_activity.read();
            let idle_duration = last_activity.elapsed();

            if idle_duration >= self.peer_redis_idle_threshold {
                debug!("Peer Redis nodes connections idle for {:?}, performing health check", idle_duration);

                if let Some(peer_pool) = &self.peer_redis_pool {
                    match self.ping_peer_redis_nodes(peer_pool).await {
                        Ok(_) => {
                            debug!("Peer Redis nodes health check successful after {:?} idle time", idle_duration);
                        }
                        Err(e) => {
                            warn!("Peer Redis nodes health check failed after {:?} idle time: {}", idle_duration, e);
                        }
                    }
                }
            } else {
                debug!("Peer Redis nodes connections active (idle for {:?}), skipping health check", idle_duration);
            }
        }
    }

    /// Ping Redis connection to keep it alive
    async fn ping_redis_connection(&self, pool: &Pool) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut conn = pool.get().await?;
        let _: String = deadpool_redis::redis::cmd("PING").query_async(&mut conn).await?;
        Ok(())
    }

    /// Ping secondary node to keep connection alive - bypasses authentication for health checks
    async fn ping_secondary_node(&self, node: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Create a direct connection for health check without authentication
        match tonic::transport::Endpoint::from_shared(node.to_string()) {
            Ok(endpoint) => {
                match endpoint.connect().await {
                    Ok(channel) => {
                        let mut client = KeyValueServiceClient::new(channel);
                        let request = tonic::Request::new(PingRequest {});

                        // Use ping directly - this will go through authentication if enabled
                        // TODO: We need a separate health check endpoint that bypasses auth
                        match client.ping(request).await {
                            Ok(_) => {
                                debug!("Health check ping successful for node: {}", node);
                                Ok(())
                            }
                            Err(e) => {
                                // If authentication fails, it might be due to auth being enabled
                                // Log this as a warning but don't fail the health check completely
                                if e.code() == tonic::Code::Unauthenticated {
                                    warn!("Health check authentication failed for node {} - this is expected if auth is enabled", node);
                                    // For now, consider this a successful connection test
                                    Ok(())
                                } else {
                                    error!("Health check failed for node {}: {}", node, e);
                                    Err(Box::new(e))
                                }
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to connect to node {} for health check: {}", node, e);
                        Err(Box::new(e))
                    }
                }
            }
            Err(e) => {
                error!("Invalid endpoint for node {}: {}", node, e);
                Err(Box::new(e))
            }
        }
    }

    /// Ping site replication nodes to keep connections alive
    async fn ping_site_replication_nodes(&self, site_manager: &Arc<tokio::sync::Mutex<SiteReplicationManager>>) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let manager = site_manager.lock().await;

        // Get the primary and failover node URLs
        let primary_node = manager.get_primary_node();
        let failover_node = manager.get_failover_node();

        let mut success_count = 0;
        let mut total_count = 0;

        // Health check primary node if configured
        if !primary_node.is_empty() {
            total_count += 1;
            match self.ping_site_node(primary_node, "primary").await {
                Ok(_) => {
                    success_count += 1;
                    debug!("Site primary node {} health check successful", primary_node);
                }
                Err(e) => {
                    warn!("Site primary node {} health check failed: {}", primary_node, e);
                }
            }
        }

        // Health check failover node if configured
        if !failover_node.is_empty() {
            total_count += 1;
            match self.ping_site_node(failover_node, "failover").await {
                Ok(_) => {
                    success_count += 1;
                    debug!("Site failover node {} health check successful", failover_node);
                }
                Err(e) => {
                    warn!("Site failover node {} health check failed: {}", failover_node, e);
                }
            }
        }

        if total_count == 0 {
            debug!("No site replication nodes configured for health check");
            return Ok(());
        }

        if success_count > 0 {
            debug!("Site replication health check: {}/{} nodes successful", success_count, total_count);
            Ok(())
        } else {
            Err("All site replication nodes failed health check".into())
        }
    }

    /// Ping a specific site replication node
    async fn ping_site_node(&self, node: &str, node_type: &str) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        match tonic::transport::Endpoint::from_shared(node.to_string()) {
            Ok(endpoint) => {
                match endpoint.connect().await {
                    Ok(channel) => {
                        let mut client = KeyValueServiceClient::new(channel);
                        let request = tonic::Request::new(PingRequest {});

                        match client.ping(request).await {
                            Ok(_) => {
                                debug!("Health check ping successful for {} site node: {}", node_type, node);
                                Ok(())
                            }
                            Err(e) => {
                                if e.code() == tonic::Code::Unauthenticated {
                                    warn!("Health check authentication failed for {} site node {} - this is expected if auth is enabled", node_type, node);
                                    // Consider this a successful connection test
                                    Ok(())
                                } else {
                                    error!("Health check failed for {} site node {}: {}", node_type, node, e);
                                    Err(Box::new(e))
                                }
                            }
                        }
                    }
                    Err(e) => {
                        error!("Failed to connect to {} site node {} for health check: {}", node_type, node, e);
                        Err(Box::new(e))
                    }
                }
            }
            Err(e) => {
                error!("Invalid endpoint for {} site node {}: {}", node_type, node, e);
                Err(Box::new(e))
            }
        }
    }

    /// Ping peer Redis nodes to keep connections alive
    async fn ping_peer_redis_nodes(&self, peer_pool: &PeerRedisPool) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Get pool statistics to verify connections are healthy
        let stats = peer_pool.get_pool_stats();

        for (node_url, (current_size, max_size)) in stats {
            debug!("Peer Redis node {} pool status: {}/{}", node_url, current_size, max_size);

            // Perform a simple ping operation to keep connections alive
            if peer_pool.ping_node(&node_url).await {
                debug!("Peer Redis node {} ping successful", node_url);
            } else {
                warn!("Peer Redis node {} ping failed", node_url);
            }
        }

        Ok(())
    }
}
