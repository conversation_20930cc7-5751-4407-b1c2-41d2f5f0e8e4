# Test configuration for idle-based health checks
redis_url = "redis://settlenxt:npci@127.0.0.1:6379"
secondary_nodes = ["http://127.0.0.1:50052"]
port = 50051
redis_pool_size = 256
replication_factor = 1
read_consistency = 0
async_replication = true
max_retries = 3
retry_delay_ms = 100
replication_batch_max_age_secs = 5
secondary_pool_size = 256
max_batch_size = 1000
batch_flush_interval_ms = 100
tcp_keepalive_secs = 60
tcp_nodelay = true
concurrency_limit = 1000
max_concurrent_streams = 1000
chunk_size = 100
num_shards = 64
worker_threads = 8

# Authentication disabled for testing
auth_enabled = false
auth_username = "admin"
auth_password = "password"
session_duration_secs = 3600
auth_mode = "connection_only"
auth_token_expiry_enabled = false

# Write consistency disabled for testing
peer_redis_nodes = []
write_consistency = ""
quorum_value = 1
write_retry_count = 3
peer_redis_pool_size = 256

# Site replication disabled for testing
site_replication_enabled = false
site_primary_node = ""
site_failover_node = ""
site_replication_retry_count = 3
site_replication_timeout_ms = 5000
site_replication_pool_size = 256
use_physical_connections = true

# IDLE-BASED HEALTH CHECK CONFIGURATION:

# Enable Redis connection health checks to keep connections alive during idle periods
redis_keepalive_enabled = true

# Interval in seconds for checking if Redis connections need health checks
redis_keepalive_interval_secs = 10

# Idle threshold in seconds - health checks only run if connections are idle for this duration
redis_idle_threshold_secs = 20

# Enable secondary nodes health checks to keep connections alive during idle periods
secondary_nodes_keepalive_enabled = true

# Interval in seconds for checking if secondary node connections need health checks
secondary_nodes_keepalive_interval_secs = 10

# Idle threshold in seconds - health checks only run if connections are idle for this duration
secondary_nodes_idle_threshold_secs = 20

# Enable site replication nodes health checks to keep connections alive during idle periods
site_nodes_keepalive_enabled = true

# Interval in seconds for checking if site replication connections need health checks
site_nodes_keepalive_interval_secs = 10

# Idle threshold in seconds - health checks only run if connections are idle for this duration
site_nodes_idle_threshold_secs = 20

# Enable peer Redis nodes health checks to keep connections alive during idle periods
peer_redis_keepalive_enabled = true

# Interval in seconds for checking if peer Redis connections need health checks
peer_redis_keepalive_interval_secs = 10

# Idle threshold in seconds - health checks only run if connections are idle for this duration
peer_redis_idle_threshold_secs = 20
